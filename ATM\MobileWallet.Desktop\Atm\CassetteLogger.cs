using MobileWallet.Desktop.API;
using System;
using System.Collections.Generic;
using System.Linq;

namespace MobileWallet.Desktop.Atm
{
    /// <summary>
    /// Specialized logging utility for cassette operations and diagnostics
    /// </summary>
    public static class CassetteLogger
    {
        /// <summary>
        /// Log detailed cassette status information
        /// </summary>
        public static void LogCassetteStatus(List<CassetteDTO> cassettes, string operation = "")
        {
            try
            {
                var prefix = string.IsNullOrEmpty(operation) ? "Cassette Status" : $"Cassette Status ({operation})";
                
                _ = App.LogError($"{prefix}: Total cassettes: {cassettes.Count}", LogType.Withdraw, null);
                
                foreach (var cassette in cassettes.OrderBy(c => c.No))
                {
                    var statusIcon = GetStatusIcon(cassette.Status);
                    _ = App.LogError($"  C{cassette.No}: {cassette.Status} {statusIcon} [ID: {cassette.Id}]", LogType.Withdraw, null);
                    
                    // Special attention to 2K cassette
                    if (cassette.No == 2)
                    {
                        if (cassette.Status != "No Issues" && cassette.Status != "Low Level")
                        {
                            _ = App.LogError($"  ⚠️  2K CASSETTE ISSUE DETECTED: {cassette.Status}", LogType.Error, null);
                        }
                        else
                        {
                            _ = App.LogError($"  ✅ 2K Cassette operational", LogType.Withdraw, null);
                        }
                    }
                }
                
                // Summary
                var healthyCassettes = cassettes.Count(c => c.Status == "No Issues" || c.Status == "Low Level");
                var healthPercentage = cassettes.Count > 0 ? (healthyCassettes * 100 / cassettes.Count) : 0;
                _ = App.LogError($"{prefix} Summary: {healthyCassettes}/{cassettes.Count} healthy ({healthPercentage}%)", LogType.Withdraw, null);
            }
            catch (Exception ex)
            {
                _ = App.LogError($"Error logging cassette status: {ex.Message}", LogType.Error, ex.StackTrace);
            }
        }

        /// <summary>
        /// Log denomination calculation details
        /// </summary>
        public static void LogDenominationCalculation(List<DenominationRecordDto> denominations, int targetAmount, List<DenominationRecordDto> result)
        {
            try
            {
                _ = App.LogError($"=== DENOMINATION CALCULATION ===", LogType.Withdraw, null);
                _ = App.LogError($"Target Amount: {targetAmount}", LogType.Withdraw, null);
                
                _ = App.LogError("Available Denominations:", LogType.Withdraw, null);
                foreach (var denom in denominations.OrderByDescending(d => d.Denomination))
                {
                    var cassetteInfo = $"C{denom.CassetteNo}";
                    _ = App.LogError($"  {cassetteInfo}: {denom.Denomination} x {denom.Count} = {denom.Denomination * denom.Count}", LogType.Withdraw, null);
                }
                
                _ = App.LogError("Calculated Distribution:", LogType.Withdraw, null);
                var totalDispensed = 0;
                var hasNotes = false;
                
                foreach (var note in result.Where(r => r.Count > 0).OrderByDescending(r => r.Denomination))
                {
                    hasNotes = true;
                    var value = note.Denomination * note.Count;
                    totalDispensed += value;
                    var cassetteInfo = $"C{note.CassetteNo}";
                    _ = App.LogError($"  {cassetteInfo}: {note.Denomination} x {note.Count} = {value}", LogType.Withdraw, null);
                    
                    // Special logging for 2K cassette
                    if (note.CassetteNo == "2" && note.Count > 0)
                    {
                        _ = App.LogError($"  🎯 2K Cassette will dispense {note.Count} notes (value: {value})", LogType.Withdraw, null);
                    }
                }
                
                if (!hasNotes)
                {
                    _ = App.LogError("  ❌ NO VALID COMBINATION FOUND", LogType.Error, null);
                }
                else
                {
                    _ = App.LogError($"Total to dispense: {totalDispensed} (Target: {targetAmount}) ✅", LogType.Withdraw, null);
                }
                
                _ = App.LogError($"=== END CALCULATION ===", LogType.Withdraw, null);
            }
            catch (Exception ex)
            {
                _ = App.LogError($"Error logging denomination calculation: {ex.Message}", LogType.Error, ex.StackTrace);
            }
        }

        /// <summary>
        /// Log dispenser command execution
        /// </summary>
        public static void LogDispenserCommand(string command, string parameters, string? result, int attempt = 1, int maxAttempts = 1)
        {
            try
            {
                var attemptInfo = maxAttempts > 1 ? $" (Attempt {attempt}/{maxAttempts})" : "";
                _ = App.LogError($"DISPENSER CMD: {command}{attemptInfo}", LogType.Withdraw, null);
                _ = App.LogError($"  Parameters: {parameters}", LogType.Withdraw, null);
                
                if (result == null)
                {
                    _ = App.LogError($"  Result: SUCCESS ✅", LogType.Withdraw, null);
                }
                else
                {
                    var severity = IsErrorSevere(result) ? LogType.Error : LogType.Withdraw;
                    _ = App.LogError($"  Result: ERROR - {result} ❌", severity, null);
                    
                    // Special handling for 2K cassette related errors
                    if (command == "move_forward" && parameters.Contains("C2:") && !parameters.Contains("C2:0"))
                    {
                        _ = App.LogError($"  ⚠️  2K CASSETTE COMMAND FAILED: {result}", LogType.Error, null);
                    }
                }
            }
            catch (Exception ex)
            {
                _ = App.LogError($"Error logging dispenser command: {ex.Message}", LogType.Error, ex.StackTrace);
            }
        }

        /// <summary>
        /// Log withdrawal transaction details
        /// </summary>
        public static void LogWithdrawalTransaction(int amount, List<CassetteDTO> initialStatus, List<DenominationRecordDto> plannedDistribution, string? finalResult)
        {
            try
            {
                _ = App.LogError($"=== WITHDRAWAL TRANSACTION ===", LogType.Withdraw, null);
                _ = App.LogError($"Amount: {amount}", LogType.Withdraw, null);
                _ = App.LogError($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}", LogType.Withdraw, null);
                
                // Log initial cassette health
                var healthyCassettes = initialStatus.Count(c => c.Status == "No Issues" || c.Status == "Low Level");
                _ = App.LogError($"Initial Cassette Health: {healthyCassettes}/{initialStatus.Count}", LogType.Withdraw, null);
                
                // Log 2K cassette specific info
                var cassette2 = initialStatus.FirstOrDefault(c => c.No == 2);
                if (cassette2 != null)
                {
                    _ = App.LogError($"2K Cassette Status: {cassette2.Status}", LogType.Withdraw, null);
                }
                
                // Log planned distribution
                var cassette2Notes = plannedDistribution.FirstOrDefault(d => d.CassetteNo == "2");
                if (cassette2Notes != null && cassette2Notes.Count > 0)
                {
                    _ = App.LogError($"2K Cassette Planned: {cassette2Notes.Count} notes", LogType.Withdraw, null);
                }
                
                // Log final result
                if (finalResult == null)
                {
                    _ = App.LogError($"Transaction Result: SUCCESS ✅", LogType.Withdraw, null);
                }
                else
                {
                    _ = App.LogError($"Transaction Result: FAILED - {finalResult} ❌", LogType.Error, null);
                }
                
                _ = App.LogError($"=== END TRANSACTION ===", LogType.Withdraw, null);
            }
            catch (Exception ex)
            {
                _ = App.LogError($"Error logging withdrawal transaction: {ex.Message}", LogType.Error, ex.StackTrace);
            }
        }

        /// <summary>
        /// Get visual status icon for cassette status
        /// </summary>
        private static string GetStatusIcon(string status)
        {
            return status switch
            {
                "No Issues" => "✅",
                "Low Level" => "⚠️",
                "Empty Cassette" => "❌",
                "Cassette Out Error" => "🔴",
                "Cassette Not Identified" => "❓",
                _ => "⚠️"
            };
        }

        /// <summary>
        /// Determine if an error is severe
        /// </summary>
        private static bool IsErrorSevere(string error)
        {
            var severeErrors = new[]
            {
                "Empty Cassette",
                "Cassette Out Error",
                "Cassette Not Identified",
                "Main Motor Failure",
                "Cassette internal failure",
                "Jam in note feeder transport"
            };
            
            return severeErrors.Any(severe => error.Contains(severe));
        }
    }
}
