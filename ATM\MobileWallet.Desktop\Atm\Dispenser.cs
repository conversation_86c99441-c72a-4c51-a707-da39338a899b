﻿using System.IO.Ports;
using MobileWallet.Desktop.API;
using Python.Runtime;

namespace MobileWallet.Desktop.Atm
{
    public class PythonDispenser
    {
        private String _latestResponse = "";

        // Initialize the hardware
        public bool Initialize()
        {
            try
            {
                ReadCassette();
                return true;
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e,e.Message);
                Console.Write(e);
            }
            return false;
        }

        public string RunCommand(string portName, string message)
        {
            using (Py.GIL())
            {
                // Import the Python module
                dynamic pyModule = Py.Import("app_serial");
                // Define the port and command bytes
                // Call the main function from Python script
                _ = App.LogError("Request: " + message, LogType.Withdraw);
                string response = pyModule.main(portName, message + '\r');
                _ = App.LogError("Response: " + response, LogType.Withdraw);
                if (!string.IsNullOrWhiteSpace(response))
                {
                    _latestResponse = response;
                    return response;
                }
            }
            return "";
        }

        private void SerialPortOnErrorReceived(object sender, SerialErrorReceivedEventArgs e)
        {
            Console.WriteLine("Received Error: " + e.EventType.ToString());
        }

        public SerialPort? Initialize(
            string portName,
            int baudRate = 9600,
            int dataBits = 8,
            Parity parity = Parity.Even
        )
        {
            try
            {
                var serialPort = new SerialPort(portName, baudRate, parity);
                serialPort.Handshake = Handshake.None;
                serialPort.DataBits = dataBits;
                serialPort.StopBits = StopBits.One;
                serialPort.Open();
                return serialPort;
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e,e.Message);
                Console.Write(e);
            }
            return null;
        }

        static void SendCommand(SerialPort serialPort, string hexCommand)
        {
            // Convert hex string to byte array
            byte[] commandBytes = HexStringToByteArray(hexCommand);
            // Send command to the control board
            serialPort.Write(commandBytes, 0, commandBytes.Length);
            Console.WriteLine("Sent: " + hexCommand);
        }

        static string ReadResponse(SerialPort serialPort)
        {
            // Wait for response data
            Thread.Sleep(100); // Adjust this delay if necessary

            int bytesToRead = serialPort.BytesToRead;
            byte[] buffer = new byte[bytesToRead];
            serialPort.Read(buffer, 0, bytesToRead);

            // Convert response bytes to hex string
            return BitConverter.ToString(buffer).Replace("-", " ");
        }

        static byte[] HexStringToByteArray(string hex)
        {
            string[] hexValues = hex.Split(' ');
            byte[] bytes = new byte[hexValues.Length];
            for (int i = 0; i < hexValues.Length; i++)
            {
                bytes[i] = Convert.ToByte(hexValues[i], 16);
            }
            return bytes;
        }

        public bool StartShutter()
        {
            SerialPort? serialPort = null;
            try
            {
                serialPort = Initialize("COM6", 19200, 8, Parity.None);
                if (serialPort == null)
                {
                    return false;
                }

                string command = "10 02 03 00 43 37 01 10 03 76";
                SendCommand(serialPort, command);
                var response = ReadResponse(serialPort);
                if (response == "10 06")
                {
                    command = "10 05";
                    Thread.Sleep(2000);
                    SendCommand(serialPort, command);
                    response = ReadResponse(serialPort);
                    if (response.Contains("10 02 50 02 00 37 01 10 03 34"))
                    {
                        return true;
                    }
                }
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e,e.Message);
                Console.WriteLine(e);
            }
            finally { }

            return false;
        }

        public bool StopShutter()
        {
            SerialPort? serialPort = null;
            try
            {
                serialPort = Initialize("COM6", 19200, 8, Parity.None);
                if (serialPort == null)
                {
                    return false;
                }
                string command = "10 02 03 00 43 38 01 10 03 79";
                SendCommand(serialPort, command);
                var response = ReadResponse(serialPort);
                if (response == "10 06")
                {
                    command = "10 05";
                    SendCommand(serialPort, command);
                    response = ReadResponse(serialPort);
                    if (response == "10 02 50 02 00 38 01 10 03 3B")
                    {
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                Console.WriteLine(ex);
            }
            finally { }

            return false;
        }

        /// <summary>
        /// Open Cassette Command If Null means Success else Error
        /// </summary>
        /// <returns></returns>
        public string? OpenCassette()
        {
            try
            {
                string command = LrcCalculate("8");
                WriteToSerialPort(command);
                var r = DisplayResult(_latestResponse);
                return r;
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                return ex.Message;
            }
        }

        /// <summary>
        /// Close Cassette Command If Null means Success else Error
        /// </summary>
        /// <returns></returns>
        public string? CloseCassette()
        {
            try
            {
                string command = LrcCalculate("7");
                WriteToSerialPort(command + Environment.NewLine);
                return DisplayResult(_latestResponse);
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                return ex.Message;
            }
        }

        /// <summary>
        /// Reset Command If Null means Success else Error
        /// </summary>
        /// <returns></returns>
        public string? Reset()
        {
            try
            {
                string command = LrcCalculate("0");
                WriteToSerialPort(command);
                return DisplayResult(_latestResponse);
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                return ex.Message;
            }
        }

        // Take cash from cassettes

        public string? move_forward(
            string cassette1,
            string cassette2,
            string cassette3,
            string cassette4
        )
        {
            try
            {
                string commandString = "20";
                if (cassette1 != "0")
                {
                    commandString = commandString + "1" + cassette1.PadLeft(3, '0');
                }
                if (cassette2 != "0")
                {
                    commandString = commandString + "2" + cassette2.PadLeft(3, '0');
                }
                if (cassette3 != "0")
                {
                    commandString = commandString + "3" + cassette3.PadLeft(3, '0');
                }
                if (cassette4 != "0")
                {
                    commandString = commandString + "4" + cassette4.PadLeft(3, '0');
                }
                string command = LrcCalculate(commandString);
                WriteToSerialPort(command);
                return DisplayResult(_latestResponse);
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                return ex.Message;
            }
        }

        // Deliver Notes
        public string? deliver_notes()
        {
            try
            {
                string command = LrcCalculate("3");
                WriteToSerialPort(command + Environment.NewLine);
                return DisplayResult(_latestResponse);
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                return ex.Message;
            }
        }

        private static string LrcCalculate(string message)
        {
            Console.WriteLine("Command String: " + message);
            // Step 1: Calculate the "exclusive or" of all characters in the string
            byte xorResult = 0;
            foreach (char character in message)
            {
                xorResult ^= Convert.ToByte(character);
            }

            // Step 2: Divide the hexadecimal value by 0x10 and truncate the result
            byte y = (byte)(xorResult / 0x10);

            // Step 3: Calculate the "logical and" between the result and 0x0F
            byte z = (byte)(xorResult & 0x0F);

            // Step 4: Add 0x30 to the last two values to get L1 and L2
            byte l1 = (byte)(y | 0x30);
            byte l2 = (byte)(z | 0x30);

            // Concatenate the original message with L1 and L2
            string finalOutput = message + Convert.ToChar(l1) + Convert.ToChar(l2);

            return finalOutput;
        }

        public string? DisplayResult(string responses)
        {
            if (responses == "")
            {
                return "Response Timeout";
            }
            var response = responses[0];
            string? result = "";
            if (response == '0')
            {
                result = null;
            }
            else if (response == '1')
            {
                result = "Low Level";
            }
            else if (response == '2')
            {
                result = "Empty Cassette";
            }
            else if (response == '3')
            {
                result = "Machine not opened";
            }
            else if (response == '4')
            {
                result = "Rejected Notes";
            }
            else if (response == '5')
            {
                result = "Diverter Failure";
            }
            else if (response == '6')
            {
                result = "Failure to feed";
            }
            else if (response == '7')
            {
                result = "Transmission Error";
            }
            else if (response == '8')
            {
                result = "Illeg Com or Com Seq";
            }
            else if (response == '9')
            {
                result = "Jam in note qualifier";
            }
            else if (response == ':')
            {
                result = "NC not press or prop ins";
            }
            else if (response == '<')
            {
                result = "No notes retracted";
            }
            else if (response == '?')
            {
                result = "RV not Pres or Prop Ins";
            }
            else if (response == '@')
            {
                result = "Delivery Failure";
            }
            else if (response == 'A')
            {
                result = "Reject Failure";
            }
            else if (response == 'B')
            {
                result = "Too many notes req";
            }
            else if (response == 'C')
            {
                result = "Jam in note feeder transport";
            }
            else if (response == 'D')
            {
                result = "Reject vault almost full";
            }
            else if (response == 'E')
            {
                result = "Cassette internal failure";
            }
            else if (response == 'F')
            {
                result = "Main Motor Failure";
            }
            else if (response == 'G')
            {
                result = "Rejected Cheque";
            }
            else if (response == 'I')
            {
                result = "Note Qualifier Faulty";
            }
            else if (response == 'J')
            {
                result = "NF exit sensor failure";
            }
            else if (response == 'K')
            {
                result = "Shutter Failure";
            }
            else if (response == 'M')
            {
                result = "Notes in bundle output unit";
            }
            else if (response == 'N')
            {
                result = "Communications Timeout";
            }
            else if (response == 'P')
            {
                result = "Shutter Failure";
            }
            else if (response == 'Q')
            {
                result = "Cassette Not Identified";
            }
            else if (response == 'W')
            {
                result = "Error in throat";
            }
            else if (response == '[')
            {
                result = "Sensor Error";
            }
            else if (response == '\'')
            {
                result = "NMD Internal Failure";
            }
            else if (response == 'a')
            {
                result = "Cassette Lock Faulty";
            }
            else if (response == 'b')
            {
                result = "Error in note stacking area";
            }
            else if (response == 'c')
            {
                result = "Module need service";
            }
            else if (response == 'e')
            {
                result = "No Message to resend";
            }
            else if (response == 'p')
            {
                result = "Cassette Out Error";
            }
            _latestResponse = "";
            return result;
        }

        private bool WriteToSerialPort(string command)
        {
            try
            {
                RunCommand("COM2", command);
                return true;
                // if (_serialPort != null)
                // {
                //     _serialPort.DiscardInBuffer();
                //     _serialPort.DiscardOutBuffer();
                //     _serialPort.WriteLine(command);
                //     return true;
                // }
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e,e.Message);
                Console.WriteLine(e);
            }
            return false;
        }

        public string? ReadCassette()
        {
            try
            {
                string command = LrcCalculate("5");
                WriteToSerialPort(command);
                return DisplayResult(_latestResponse);
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                return ex.Message;
            }
        }

        public List<CassetteDTO> ReadCassetteStatus()
        {
            try
            {
                if (!Global.UseHardware)
                {
                    return new List<CassetteDTO>()
                    {
                        new CassetteDTO()
                        {
                            Id = "1",
                            No = 1,
                            Status = "No Issues",
                        },
                        new CassetteDTO()
                        {
                            Id = "1",
                            No = 2,
                            Status = "No Issues",
                        },
                        new CassetteDTO()
                        {
                            Id = "1",
                            No = 3,
                            Status = "No Issues",
                        },
                    };
                }

                return ReadCassetteStatusWithRetry();
            }
            catch (Exception ex)
            {
                _ = App.LogError(ex.Message, error: ex.StackTrace);
            }
            return new List<CassetteDTO>();
        }

        /// <summary>
        /// Read cassette status with retry logic for improved reliability
        /// </summary>
        private List<CassetteDTO> ReadCassetteStatusWithRetry(int maxRetries = 3)
        {
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    App.AppLogger.Info($"Reading cassette status - Attempt {attempt}/{maxRetries}");

                    string command = LrcCalculate("5");
                    WriteToSerialPort(command);
                    WaitForResponse();

                    if (string.IsNullOrEmpty(_latestResponse))
                    {
                        App.AppLogger.Warning($"Empty response on attempt {attempt}");
                        if (attempt < maxRetries)
                        {
                            Thread.Sleep(1000);
                            continue;
                        }
                        return new List<CassetteDTO>();
                    }

                    var result = CassetteStatusDecoder.DecodeCassetteStatus(_latestResponse);

                    // Validate the result
                    if (result.Any() && result.All(c => !string.IsNullOrEmpty(c.Status)))
                    {
                        App.AppLogger.Info($"Successfully read cassette status: {string.Join(", ", result.Select(c => $"C{c.No}:{c.Status}"))}");

                        // Special logging for 2K cassette (typically cassette 2)
                        var cassette2 = result.FirstOrDefault(c => c.No == 2);
                        if (cassette2 != null)
                        {
                            App.AppLogger.Info($"2K Cassette (C2) Status: {cassette2.Status}");
                            if (cassette2.Status != "No Issues" && cassette2.Status != "Low Level")
                            {
                                App.AppLogger.Warning($"2K Cassette has issue: {cassette2.Status}");
                            }
                        }

                        return result;
                    }
                    else
                    {
                        App.AppLogger.Warning($"Invalid cassette status result on attempt {attempt}");
                        if (attempt < maxRetries)
                        {
                            Thread.Sleep(1000);
                            continue;
                        }
                    }
                }
                catch (Exception ex)
                {
                    App.AppLogger.Error(ex, $"Error reading cassette status on attempt {attempt}: {ex.Message}");
                    if (attempt < maxRetries)
                    {
                        Thread.Sleep(1000);
                        continue;
                    }
                }
            }

            App.AppLogger.Error($"Failed to read cassette status after {maxRetries} attempts");
            return new List<CassetteDTO>();
        }
    }

    public class Dispenser
    {
        SerialPort? _serialPort;

        private String _latestResponse = "";

        // Initialize the hardware
        public bool Initialize()
        {
            try
            {
                Close();
                _serialPort = new SerialPort("COM4", 9600, Parity.Even);
                // _serialPort.Handshake = Handshake.None;
                _serialPort.DataBits = 7;
                _serialPort.NewLine = "\r";
                _serialPort.StopBits = StopBits.One;
                _serialPort.Handshake = Handshake.None;
                // _serialPort.RtsEnable = true;
                // _serialPort.DtrEnable = false;
                // _serialPort.ErrorReceived += SerialPortOnErrorReceived;
                // _serialPort.DataReceived += SerialPortOnDataReceived;
                _serialPort.Open();
                _serialPort.DiscardOutBuffer();
                _serialPort.DiscardInBuffer();
                return true;
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e,e.Message);
                Console.Write(e);
                return false;
            }
        }

        private void SerialPortOnErrorReceived(object sender, SerialErrorReceivedEventArgs e)
        {
            Console.WriteLine("Received Error: " + e.EventType.ToString());
        }

        private void SerialPortOnDataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            var sp = (SerialPort)sender;
            var r = sp.ReadExisting();
            _latestResponse = r;
            Console.WriteLine("Received: " + _latestResponse);
        }

        public SerialPort? Initialize(
            string portName,
            int baudRate = 9600,
            int dataBits = 8,
            Parity parity = Parity.Even
        )
        {
            try
            {
                var serialPort = new SerialPort(portName, baudRate, parity);
                serialPort.Handshake = Handshake.None;
                serialPort.DataBits = dataBits;
                serialPort.StopBits = StopBits.One;
                serialPort.Open();
                return serialPort;
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e,e.Message);
                Console.Write(e);
            }
            return null;
        }

        public void Close(SerialPort? serialPort)
        {
            try
            {
                if (serialPort != null)
                {
                    serialPort.Close();
                    serialPort.Dispose();
                    serialPort = null;
                }
            }
            catch { }
        }

        public void Close()
        {
            try
            {
                if (_serialPort != null)
                {
                    _serialPort.DataReceived -= SerialPortOnDataReceived;
                    _serialPort.Close();
                    _serialPort.Dispose();
                    _serialPort = null;
                }
            }
            catch { }
        }

        static void SendCommand(SerialPort serialPort, string hexCommand)
        {
            // Convert hex string to byte array
            byte[] commandBytes = HexStringToByteArray(hexCommand);
            // Send command to the control board
            serialPort.Write(commandBytes, 0, commandBytes.Length);
            Console.WriteLine("Sent: " + hexCommand);
        }

        static string ReadResponse(SerialPort serialPort)
        {
            // Wait for response data
            Thread.Sleep(100); // Adjust this delay if necessary

            int bytesToRead = serialPort.BytesToRead;
            byte[] buffer = new byte[bytesToRead];
            serialPort.Read(buffer, 0, bytesToRead);

            // Convert response bytes to hex string
            return BitConverter.ToString(buffer).Replace("-", " ");
        }

        static byte[] HexStringToByteArray(string hex)
        {
            string[] hexValues = hex.Split(' ');
            byte[] bytes = new byte[hexValues.Length];
            for (int i = 0; i < hexValues.Length; i++)
            {
                bytes[i] = Convert.ToByte(hexValues[i], 16);
            }
            return bytes;
        }

        public bool StartShutter()
        {
            SerialPort? serialPort = null;
            try
            {
                serialPort = Initialize("COM6", 19200, 8, Parity.None);
                if (serialPort == null)
                {
                    return false;
                }
                string command = "10 02 03 00 43 37 01 10 03 76";
                SendCommand(serialPort, command);
                var response = ReadResponse(serialPort);
                if (response == "10 06")
                {
                    command = "10 05";
                    Thread.Sleep(2000);
                    SendCommand(serialPort, command);
                    response = ReadResponse(serialPort);
                    if (response.Contains("10 02 50 02 00 37 01 10 03 34"))
                    {
                        return true;
                    }
                }

                Close(serialPort);
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                Console.WriteLine(ex);
            }
            finally
            {
                Close(serialPort);
            }

            return false;
        }

        public bool StopShutter()
        {
            SerialPort? serialPort = null;
            try
            {
                serialPort = Initialize("COM6", 19200, 8, Parity.None);
                if (serialPort == null)
                {
                    return false;
                }
                string command = "10 02 03 00 43 38 01 10 03 79";
                SendCommand(serialPort, command);
                var response = ReadResponse(serialPort);
                if (response == "10 06")
                {
                    command = "10 05";
                    SendCommand(serialPort, command);
                    response = ReadResponse(serialPort);
                    if (response == "10 02 50 02 00 38 01 10 03 3B")
                    {
                        return true;
                    }
                }

                Close(serialPort);
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                Console.WriteLine(ex);
            }
            finally
            {
                Close(serialPort);
            }

            return false;
        }

        private void WaitForResponse()
        {
            Console.WriteLine("Waiting for Response");
            bool check = false;
            while (check == false)
            {
                _latestResponse = _serialPort?.ReadExisting() ?? "";
                if (!string.IsNullOrWhiteSpace(_latestResponse))
                {
                    check = true;
                }
            }
        }

        /// <summary>
        /// Open Cassette Command If Null means Success else Error
        /// </summary>
        /// <returns></returns>
        public string? OpenCassette()
        {
            try
            {
                string command = LrcCalculate("8");
                WriteToSerialPort(command);
                WaitForResponse();
                var r = DisplayResult(_latestResponse[0]);
                return r;
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                return ex.Message;
            }
        }

        /// <summary>
        /// Close Cassette Command If Null means Success else Error
        /// </summary>
        /// <returns></returns>
        public string? CloseCassette()
        {
            try
            {
                string command = LrcCalculate("7");
                WriteToSerialPort(command + Environment.NewLine);
                WaitForResponse();
                return DisplayResult(_latestResponse[0]);
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                return ex.Message;
            }
        }

        /// <summary>
        /// Reset Command If Null means Success else Error
        /// </summary>
        /// <returns></returns>
        public string? Reset()
        {
            try
            {
                string command = LrcCalculate("0");
                WriteToSerialPort(command);
                WaitForResponse();
                return DisplayResult(_latestResponse[0]);
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                return ex.Message;
            }
        }

        // Take cash from cassettes

        public string? move_forward(
            string cassette1,
            string cassette2,
            string cassette3,
            string cassette4
        )
        {
            return ExecuteCommandWithRetry(() =>
            {
                string commandString = "20";
                if (cassette1 != "0")
                {
                    commandString = commandString + "1" + cassette1.PadLeft(3, '0');
                }
                if (cassette2 != "0")
                {
                    commandString = commandString + "2" + cassette2.PadLeft(3, '0');
                }
                if (cassette3 != "0")
                {
                    commandString = commandString + "3" + cassette3.PadLeft(3, '0');
                }
                if (cassette4 != "0")
                {
                    commandString = commandString + "4" + cassette4.PadLeft(3, '0');
                }

                App.AppLogger.Info($"Executing move_forward command: {commandString} (C1:{cassette1}, C2:{cassette2}, C3:{cassette3}, C4:{cassette4})");

                string command = LrcCalculate(commandString);
                WriteToSerialPort(command);
                WaitForResponse();
                return DisplayResult(_latestResponse[0]);
            }, "move_forward", $"C1:{cassette1}, C2:{cassette2}, C3:{cassette3}, C4:{cassette4}");
        }

        /// <summary>
        /// Execute dispenser command with retry logic for improved reliability
        /// </summary>
        private string? ExecuteCommandWithRetry(Func<string?> commandAction, string commandName, string parameters, int maxRetries = 3)
        {
            string? lastError = null;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    App.AppLogger.Info($"Executing {commandName} - Attempt {attempt}/{maxRetries} - Parameters: {parameters}");

                    var result = commandAction();

                    if (result == null) // Success
                    {
                        if (attempt > 1)
                        {
                            App.AppLogger.Info($"{commandName} succeeded on attempt {attempt}");
                        }
                        return null;
                    }

                    // Check if this is a recoverable error
                    if (IsRecoverableError(result))
                    {
                        App.AppLogger.Warning($"{commandName} attempt {attempt} failed with recoverable error: {result}");
                        lastError = result;

                        if (attempt < maxRetries)
                        {
                            // Perform mini-recovery between attempts
                            PerformMiniRecovery(result);
                            Thread.Sleep(1000 * attempt); // Progressive delay
                            continue;
                        }
                    }
                    else
                    {
                        // Non-recoverable error, return immediately
                        App.AppLogger.Error($"{commandName} failed with non-recoverable error: {result}");
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    App.AppLogger.Error(ex, $"{commandName} attempt {attempt} threw exception: {ex.Message}");
                    lastError = ex.Message;

                    if (attempt < maxRetries)
                    {
                        Thread.Sleep(1000 * attempt);
                        continue;
                    }
                }
            }

            App.AppLogger.Error($"{commandName} failed after {maxRetries} attempts. Last error: {lastError}");
            return lastError;
        }

        /// <summary>
        /// Determine if an error is recoverable and worth retrying
        /// </summary>
        private bool IsRecoverableError(string error)
        {
            if (string.IsNullOrEmpty(error)) return false;

            var recoverableErrors = new[]
            {
                "Response Timeout",
                "Transmission Error",
                "No Message to resend",
                "Communications Timeout"
            };

            return recoverableErrors.Any(e => error.Contains(e));
        }

        /// <summary>
        /// Perform mini-recovery between retry attempts
        /// </summary>
        private void PerformMiniRecovery(string error)
        {
            try
            {
                App.AppLogger.Info($"Performing mini-recovery for error: {error}");

                if (error.Contains("Timeout") || error.Contains("Transmission"))
                {
                    // Clear communication buffers
                    if (_serialPort != null)
                    {
                        _serialPort.DiscardInBuffer();
                        _serialPort.DiscardOutBuffer();
                    }
                    Thread.Sleep(500);
                }

                // Reset latest response
                _latestResponse = "";
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex, $"Error during mini-recovery: {ex.Message}");
            }
        }

        // Deliver Notes

        public string? deliver_notes()
        {
            try
            {
                string command = LrcCalculate("3");
                WriteToSerialPort(command + Environment.NewLine);
                WaitForResponse();
                return DisplayResult(_latestResponse[0]);
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                return ex.Message;
            }
        }

        private static string LrcCalculate(string message)
        {
            Console.WriteLine("Command String: " + message);
            // Step 1: Calculate the "exclusive or" of all characters in the string
            byte xorResult = 0;
            foreach (char character in message)
            {
                xorResult ^= Convert.ToByte(character);
            }

            // Step 2: Divide the hexadecimal value by 0x10 and truncate the result
            byte y = (byte)(xorResult / 0x10);

            // Step 3: Calculate the "logical and" between the result and 0x0F
            byte z = (byte)(xorResult & 0x0F);

            // Step 4: Add 0x30 to the last two values to get L1 and L2
            byte l1 = (byte)(y | 0x30);
            byte l2 = (byte)(z | 0x30);

            // Concatenate the original message with L1 and L2
            string finalOutput = message + Convert.ToChar(l1) + Convert.ToChar(l2);

            return finalOutput;
        }

        public string? DisplayResult(char response)
        {
            string? result = "";
            if (response == '0')
            {
                result = null;
            }
            else if (response == '1')
            {
                result = "Low Level";
            }
            else if (response == '2')
            {
                result = "Empty Cassette";
            }
            else if (response == '3')
            {
                result = "Machine not opened";
            }
            else if (response == '4')
            {
                result = "Rejected Notes";
            }
            else if (response == '5')
            {
                result = "Diverter Failure";
            }
            else if (response == '6')
            {
                result = "Failure to feed";
            }
            else if (response == '7')
            {
                result = "Transmission Error";
            }
            else if (response == '8')
            {
                result = "Illeg Com or Com Seq";
            }
            else if (response == '9')
            {
                result = "Jam in note qualifier";
            }
            else if (response == ':')
            {
                result = "NC not press or prop ins";
            }
            else if (response == '<')
            {
                result = "No notes retracted";
            }
            else if (response == '?')
            {
                result = "RV not Pres or Prop Ins";
            }
            else if (response == '@')
            {
                result = "Delivery Failure";
            }
            else if (response == 'A')
            {
                result = "Reject Failure";
            }
            else if (response == 'B')
            {
                result = "Too many notes req";
            }
            else if (response == 'C')
            {
                result = "Jam in note feeder transport";
            }
            else if (response == 'D')
            {
                result = "Reject vault almost full";
            }
            else if (response == 'E')
            {
                result = "Cassette internal failure";
            }
            else if (response == 'F')
            {
                result = "Main Motor Failure";
            }
            else if (response == 'G')
            {
                result = "Rejected Cheque";
            }
            else if (response == 'I')
            {
                result = "Note Qualifier Faulty";
            }
            else if (response == 'J')
            {
                result = "NF exit sensor failure";
            }
            else if (response == 'K')
            {
                result = "Shutter Failure";
            }
            else if (response == 'M')
            {
                result = "Notes in bundle output unit";
            }
            else if (response == 'N')
            {
                result = "Communications Timeout";
            }
            else if (response == 'P')
            {
                result = "Shutter Failure";
            }
            else if (response == 'Q')
            {
                result = "Cassette Not Identified";
            }
            else if (response == 'W')
            {
                result = "Error in throat";
            }
            else if (response == '[')
            {
                result = "Sensor Error";
            }
            else if (response == '\'')
            {
                result = "NMD Internal Failure";
            }
            else if (response == 'a')
            {
                result = "Cassette Lock Faulty";
            }
            else if (response == 'b')
            {
                result = "Error in note stacking area";
            }
            else if (response == 'c')
            {
                result = "Module need service";
            }
            else if (response == 'e')
            {
                result = "No Message to resend";
            }
            else if (response == 'p')
            {
                result = "Cassette Out Error";
            }
            _latestResponse = "";
            return result;
        }

        private bool WriteToSerialPort(string command)
        {
            try
            {
                if (_serialPort != null)
                {
                    _serialPort.DiscardInBuffer();
                    _serialPort.DiscardOutBuffer();
                    _serialPort.WriteLine(command);
                    return true;
                }
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e,e.Message);
                Console.WriteLine(e);
            }
            return false;
        }

        public string? ReadCassette()
        {
            try
            {
                string command = LrcCalculate("5");
                WriteToSerialPort(command);
                WaitForResponse();
                return DisplayResult(_latestResponse[0]);
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                return ex.Message;
            }
        }
    }
}
