using ITL;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Client;
using MobileWallet.Desktop.Extensions;

namespace MobileWallet.Desktop.Atm;

public class WithdrawHelperV2 { }

public abstract class WithdrawHelper
{
    public static async Task<bool> IsAvailableCurrencyV2(int amount)
    {
        List<DenominationRecordDto> list = App
            .CashDevice.currencyAssignment.Select(p => new DenominationRecordDto()
            {
                CassetteNo = p.Channel.ToString(),
                Denomination = (int)p.Value / 100,
                Count = (int)p.Stored,
            })
            .ToList();
        list = list.OrderByDescending(p => p.Denomination).ToList();
        var notes = GetNotesV2(list, amount);
        return notes.Any(p => p.Count > 0);
    }

    public static List<DenominationRecordDto> GetNotesV2(
        List<DenominationRecordDto> list,
        int remaining
    )
    {
        if (!list.Any())
        {
            return new List<DenominationRecordDto>();
        }

        // Use the same robust algorithm as GetNotes but without cassette status filtering
        // since V2 gets data directly from CashDevice
        _ = App.LogError($"GetNotesV2: Calculating for amount {remaining}", LogType.Withdraw, null);
        return CalculateOptimalNoteDistribution(list, remaining);
    }

    public static async Task<bool> IsAvailableCurrency(int amount, List<CassetteDTO> cassettes)
    {
        try
        {
            // Enhanced validation before checking currency availability
            if (!await ValidateCassetteStatusConsistency(cassettes))
            {
                _ = App.LogError("Cassette status validation failed", LogType.Withdraw, null);
                return false;
            }

            var transactionClient = new DenominationClient(HttpClientSingleton.Instance);
            var response = (await transactionClient.Denomination_GetDenominationByAtmAsync());
            List<DenominationRecordDto> list = response.Data?.ToList() ?? [];
            list = list.Where(p => p.CassetteNo != "4").OrderByDescending(p => p.Denomination).ToList();

            _ = App.LogError($"Checking currency availability for amount: {amount}", LogType.Withdraw, null);
            _ = App.LogError($"Available denominations: {string.Join(", ", list.Select(d => $"{d.Denomination}x{d.Count}"))}", LogType.Withdraw, null);

            var notes = GetNotes(list, amount, cassettes);
            var isAvailable = notes.Any(p => p.Count > 0);

            _ = App.LogError($"Currency availability result: {isAvailable}", LogType.Withdraw, null);
            return isAvailable;
        }
        catch (Exception ex)
        {
            _ = App.LogError($"Error checking currency availability: {ex.Message}", LogType.Error, ex.StackTrace);
            return false;
        }
    }

    /// <summary>
    /// Validate cassette status consistency to prevent issues during withdrawal
    /// </summary>
    public static async Task<bool> ValidateCassetteStatusConsistency(List<CassetteDTO> cassettes)
    {
        try
        {
            _ = App.LogError("Starting cassette status validation", LogType.Withdraw, null);

            if (!cassettes.Any())
            {
                _ = App.LogError("No cassette status data available", LogType.Withdraw, null);
                return false;
            }

            // Check for critical cassette issues
            var criticalIssues = cassettes.Where(c =>
                c.Status != "No Issues" &&
                c.Status != "Low Level" &&
                !string.IsNullOrEmpty(c.Status)).ToList();

            if (criticalIssues.Any())
            {
                _ = App.LogError($"Critical cassette issues detected: {string.Join(", ", criticalIssues.Select(c => $"C{c.No}:{c.Status}"))}", LogType.Withdraw, null);

                // Special handling for 2K cassette (typically cassette 2)
                var cassette2Issue = criticalIssues.FirstOrDefault(c => c.No == 2);
                if (cassette2Issue != null)
                {
                    _ = App.LogError($"2K Cassette (C2) has critical issue: {cassette2Issue.Status}", LogType.Withdraw, null);

                    // Attempt recovery for 2K cassette
                    if (await AttemptCassetteRecovery(2))
                    {
                        _ = App.LogError("2K Cassette recovery successful, re-reading status", LogType.Withdraw, null);
                        // Re-read status after recovery
                        var newStatus = App.Dispenser.ReadCassetteStatus();
                        var newCassette2 = newStatus.FirstOrDefault(c => c.No == 2);
                        if (newCassette2 != null && (newCassette2.Status == "No Issues" || newCassette2.Status == "Low Level"))
                        {
                            _ = App.LogError("2K Cassette recovery confirmed successful", LogType.Withdraw, null);
                            return true;
                        }
                    }

                    return false;
                }
            }

            // Validate that we have at least one working cassette
            var workingCassettes = cassettes.Where(c => c.Status == "No Issues" || c.Status == "Low Level").Count();
            if (workingCassettes == 0)
            {
                _ = App.LogError("No working cassettes available", LogType.Withdraw, null);
                return false;
            }

            _ = App.LogError($"Cassette validation passed: {workingCassettes}/{cassettes.Count} cassettes working", LogType.Withdraw, null);
            return true;
        }
        catch (Exception ex)
        {
            _ = App.LogError($"Error during cassette validation: {ex.Message}", LogType.Error, ex.StackTrace);
            return false;
        }
    }

    /// <summary>
    /// Attempt recovery for a specific cassette
    /// </summary>
    private static async Task<bool> AttemptCassetteRecovery(int cassetteNumber)
    {
        try
        {
            _ = App.LogError($"Attempting recovery for cassette {cassetteNumber}", LogType.Withdraw, null);

            // Perform targeted recovery sequence
            App.Dispenser.Reset();
            await Task.Delay(2000);

            App.Dispenser.OpenCassette();
            await Task.Delay(1000);

            App.Dispenser.ReadCassette();
            await Task.Delay(1500);

            // Verify recovery
            var status = App.Dispenser.ReadCassetteStatus();
            var targetCassette = status.FirstOrDefault(c => c.No == cassetteNumber);

            if (targetCassette != null && (targetCassette.Status == "No Issues" || targetCassette.Status == "Low Level"))
            {
                _ = App.LogError($"Cassette {cassetteNumber} recovery successful", LogType.Withdraw, null);
                return true;
            }

            _ = App.LogError($"Cassette {cassetteNumber} recovery failed, status: {targetCassette?.Status ?? "Unknown"}", LogType.Withdraw, null);
            return false;
        }
        catch (Exception ex)
        {
            _ = App.LogError($"Error during cassette {cassetteNumber} recovery: {ex.Message}", LogType.Error, ex.StackTrace);
            return false;
        }
    }

    public static List<DenominationRecordDto> GetNotes(
        List<DenominationRecordDto> list,
        int remaining,
        List<CassetteDTO> cassettes
    )
    {
        if (!list.Any())
        {
            return new List<DenominationRecordDto>();
        }

        // Apply cassette status filtering
        var availableDenominations = list.DeepCopy() ?? [];
        foreach (var denominationRecordDto in availableDenominations)
        {
            var cassette = cassettes.FirstOrDefault(p =>
                p.No == int.Parse(denominationRecordDto.CassetteNo)
            );
            if (cassette == null)
            {
                denominationRecordDto.Count = 0;
                _ = App.LogError($"Cassette {denominationRecordDto.CassetteNo} not found in status list", LogType.Withdraw, null);
            }
            else if (cassette.Status != "No Issues" && cassette.Status != "Low Level")
            {
                denominationRecordDto.Count = 0;
                _ = App.LogError($"Cassette {denominationRecordDto.CassetteNo} has status: {cassette.Status}", LogType.Withdraw, null);
            }
        }

        // Use dynamic programming approach for optimal note dispensing
        return CalculateOptimalNoteDistribution(availableDenominations, remaining);
    }

    /// <summary>
    /// Calculate optimal note distribution using dynamic programming approach
    /// This eliminates hardcoded array indexing and works with any denomination configuration
    /// </summary>
    private static List<DenominationRecordDto> CalculateOptimalNoteDistribution(
        List<DenominationRecordDto> availableDenominations,
        int targetAmount)
    {
        var result = availableDenominations.DeepCopy() ?? [];
        result.ForEach(p => p.Count = 0);

        // Sort by denomination descending for greedy approach optimization
        var sortedDenominations = availableDenominations
            .Where(d => d.Count > 0)
            .OrderByDescending(d => d.Denomination)
            .ToList();

        _ = App.LogError($"Starting note calculation for amount: {targetAmount}", LogType.Withdraw, null);
        _ = App.LogError($"Available denominations: {string.Join(", ", sortedDenominations.Select(d => $"{d.Denomination}x{d.Count}"))}", LogType.Withdraw, null);

        // Try to find a valid combination using recursive approach
        var solution = FindNoteDistribution(sortedDenominations, targetAmount, new Dictionary<int, int>(), 0);

        if (solution != null)
        {
            // Map solution back to original list
            foreach (var kvp in solution)
            {
                var originalDenom = result.FirstOrDefault(r => r.Denomination == kvp.Key);
                if (originalDenom != null)
                {
                    originalDenom.Count = kvp.Value;
                }
            }

            _ = App.LogError($"Found solution: {string.Join(", ", solution.Select(s => $"{s.Key}x{s.Value}"))}", LogType.Withdraw, null);
        }
        else
        {
            _ = App.LogError($"No valid combination found for amount: {targetAmount}", LogType.Withdraw, null);
        }

        return result;
    }

    /// <summary>
    /// Recursive function to find valid note distribution
    /// </summary>
    private static Dictionary<int, int>? FindNoteDistribution(
        List<DenominationRecordDto> denominations,
        int remainingAmount,
        Dictionary<int, int> currentSolution,
        int denominationIndex)
    {
        // Base case: exact amount reached
        if (remainingAmount == 0)
        {
            return new Dictionary<int, int>(currentSolution);
        }

        // Base case: no more denominations or negative amount
        if (denominationIndex >= denominations.Count || remainingAmount < 0)
        {
            return null;
        }

        var currentDenom = denominations[denominationIndex];

        // Try different quantities of current denomination
        for (int quantity = Math.Min(currentDenom.Count, remainingAmount / currentDenom.Denomination); quantity >= 0; quantity--)
        {
            var newSolution = new Dictionary<int, int>(currentSolution);
            if (quantity > 0)
            {
                newSolution[currentDenom.Denomination] = quantity;
            }

            var result = FindNoteDistribution(
                denominations,
                remainingAmount - (quantity * currentDenom.Denomination),
                newSolution,
                denominationIndex + 1
            );

            if (result != null)
            {
                return result;
            }
        }

        return null;
    }

    public static async Task<List<ReceiptItem>> Withdraw(int amount, List<CassetteDTO> cassettes) //chetu code
    {
        var client = new DenominationClient(HttpClientSingleton.Instance);
        var receiptData = new List<ReceiptItem>(); //chetu code

        // Enhanced logging for withdrawal transaction
        CassetteLogger.LogWithdrawalTransaction(amount, cassettes, new List<DenominationRecordDto>(), null);
        CassetteLogger.LogCassetteStatus(cassettes, "Pre-Withdrawal");

        try
        {
            if (!Global.UseHardware || App.Dispenser.Initialize())
            {
                var remaining = amount;
                var list = (await client.Denomination_GetDenominationByAtmAsync()).Data.ToList();
                list = list.Where(p => p.CassetteNo != "4").OrderByDescending(p => p.Denomination).ToList();
                var notes = GetNotes(list, remaining, cassettes);

                // Log the denomination calculation
                CassetteLogger.LogDenominationCalculation(list, remaining, notes);

                string? error = null;
                if (Global.UseHardware)
                {
                    var isError = true;
                    var tryCount = 0;
                    while (tryCount <= 1)
                    {
                        var cassette1 = notes.First(p => p.CassetteNo == "1").Count.ToString();
                        var cassette2 = notes.First(p => p.CassetteNo == "2").Count.ToString();
                        var cassette3 = notes.First(p => p.CassetteNo == "3").Count.ToString();
                        var cassette4 = "0";

                        // Enhanced logging for move_forward command
                        var parameters = $"C1:{cassette1}, C2:{cassette2}, C3:{cassette3}, C4:{cassette4}";

                        error = App.Dispenser.move_forward(
                            cassette1,
                            cassette2,
                            cassette3,
                            cassette4
                        );

                        // Log the command execution with enhanced details
                        CassetteLogger.LogDispenserCommand("move_forward", parameters, error, tryCount + 1, 2);
                        _ = App.TrackAtmRealTime(
                            new UpdateAtmRealTimeRequestModel()
                            {
                                WithdrawMoveForwardStatus = error ?? "",
                            }
                        );
                        if (error != null && error != "Low Level")
                        {
                            await HandleError(error);
                            tryCount++;
                            isError = true;
                            continue;
                        }
                        else
                        {
                            isError = false;
                        }
                        error = App.Dispenser.deliver_notes();
                        _ = App.TrackAtmRealTime(
                            new UpdateAtmRealTimeRequestModel()
                            {
                                WithdrawDeliverNotesStatus = error ?? "",
                            }
                        );
                        _ = App.LogError(
                            $"Called Deliver Notes with Result: " + error,
                            LogType.Withdraw,
                            null
                        );
                        if (error != null)
                        {
                            isError = true;
                            await HandleError(error);
                        }
                        else
                        {
                            isError = false;
                        }
                        tryCount++;
                        if (isError == false)
                        {
                            break;
                        }
                    }
                    if (isError)
                    {
                        _ = App.LogError("ATM Error", LogType.Error, error);

                        // Log failed transaction
                        CassetteLogger.LogWithdrawalTransaction(amount, cassettes, notes, error);

                        return receiptData;
                    }
                }
                receiptData.Clear();
                list = list.OrderBy(p => p.Denomination).ToList();
                // After the successful withdrawal, populate the receiptData list
                for (var index = 0; index < notes.Count; index++)
                {
                    var noteCount = notes[index];
                    var indexOfList = list.IndexOf(
                        list.First(p => p.CassetteNo == noteCount.CassetteNo)
                    );
                    var denomination = list[indexOfList].Denomination;
                    var itemAmount = denomination * noteCount.Count;
                    list[indexOfList].Count -= noteCount.Count;
                    receiptData.Add(
                        new ReceiptItem
                        {
                            Item = denomination,
                            Quantity = noteCount.Count,
                            Amount = itemAmount,
                        }
                    );
                }
                _ = App.TrackAtmRealTime(
                    new UpdateAtmRealTimeRequestModel()
                    {
                        WithdrawDeliverNotesStatus = "",
                        WithdrawMoveForwardStatus = "",
                    }
                );
                await client.Denomination_SaveDenominationsAsync(
                    new SaveDenominationRecordsRequestModel() { Records = list }
                );

                // Log successful transaction
                CassetteLogger.LogWithdrawalTransaction(amount, cassettes, notes, null);

                return receiptData;
            }
            else
            {
                return receiptData;
            }
        }
        catch (Exception ex)
        {
            App.AppLogger.Error(ex,ex.Message);
            Console.WriteLine(ex.ToString());
        }
        finally
        {
            if (Global.UseHardware) { }
        }
        return receiptData;
    }

    public static async Task HandleError(string message)
    {
        _ = App.LogError("ERROR From Dispenser: " + message, LogType.Withdraw, null);

        // Enhanced error handling with cassette-specific recovery
        await HandleErrorWithCassetteRecovery(message);
    }

    /// <summary>
    /// Enhanced error handling with specific recovery procedures for different error types
    /// </summary>
    public static async Task HandleErrorWithCassetteRecovery(string errorMessage)
    {
        _ = App.LogError($"Starting enhanced error recovery for: {errorMessage}", LogType.Withdraw, null);

        try
        {
            // Step 1: Handle shutter-specific errors
            if (errorMessage.Contains("Shutter"))
            {
                _ = App.LogError("Handling shutter error", LogType.Withdraw, null);
                App.Dispenser.StopShutter();
                await Task.Delay(1000); // Allow shutter to stop
                _ = App.LogError("Called Stop Shutter", LogType.Withdraw, null);

                App.Dispenser.StartShutter();
                await Task.Delay(1000); // Allow shutter to start
                _ = App.LogError("Called Start Shutter", LogType.Withdraw, null);
            }

            // Step 2: Handle cassette-specific errors
            if (errorMessage.Contains("Cassette") || errorMessage.Contains("Empty") ||
                errorMessage.Contains("Low Level") || errorMessage.Contains("feed"))
            {
                _ = App.LogError("Handling cassette-specific error", LogType.Withdraw, null);
                await PerformCassetteRecoverySequence();
            }

            // Step 3: General dispenser reset
            _ = App.LogError("Performing general dispenser reset", LogType.Withdraw, null);
            App.Dispenser.Reset();
            await Task.Delay(2000); // Allow reset to complete
            _ = App.LogError("Called Reset", LogType.Withdraw, null);

            // Step 4: Re-initialize cassette communication
            App.Dispenser.OpenCassette();
            await Task.Delay(1000);
            _ = App.LogError("Called OpenCassette", LogType.Withdraw, null);

            // Step 5: Read cassette status to verify recovery
            var recoveryStatus = App.Dispenser.ReadCassetteStatus();
            _ = App.LogError($"Post-recovery cassette status: {string.Join(", ", recoveryStatus.Select(c => $"C{c.No}:{c.Status}"))}", LogType.Withdraw, null);

            // Step 6: Validate recovery success
            await ValidateRecoverySuccess(recoveryStatus, errorMessage);
        }
        catch (Exception ex)
        {
            _ = App.LogError($"Error during recovery procedure: {ex.Message}", LogType.Error, ex.StackTrace);
        }
    }

    /// <summary>
    /// Perform specific recovery sequence for cassette-related issues
    /// </summary>
    private static async Task PerformCassetteRecoverySequence()
    {
        _ = App.LogError("Starting cassette recovery sequence", LogType.Withdraw, null);

        // Multiple cassette read attempts to clear any stuck states
        for (int attempt = 1; attempt <= 3; attempt++)
        {
            _ = App.LogError($"Cassette recovery attempt {attempt}/3", LogType.Withdraw, null);

            App.Dispenser.ReadCassette();
            await Task.Delay(1500);

            var status = App.Dispenser.ReadCassetteStatus();
            var problematicCassettes = status.Where(c => c.Status != "No Issues" && c.Status != "Low Level").ToList();

            if (!problematicCassettes.Any())
            {
                _ = App.LogError($"Cassette recovery successful on attempt {attempt}", LogType.Withdraw, null);
                break;
            }
            else
            {
                _ = App.LogError($"Attempt {attempt} - Still have issues: {string.Join(", ", problematicCassettes.Select(c => $"C{c.No}:{c.Status}"))}", LogType.Withdraw, null);

                if (attempt < 3)
                {
                    // Additional reset between attempts
                    App.Dispenser.Reset();
                    await Task.Delay(2000);
                }
            }
        }
    }

    /// <summary>
    /// Validate that the recovery was successful
    /// </summary>
    private static Task ValidateRecoverySuccess(List<CassetteDTO> postRecoveryStatus, string originalError)
    {
        var healthyCassettes = postRecoveryStatus.Where(c => c.Status == "No Issues" || c.Status == "Low Level").Count();
        var totalCassettes = postRecoveryStatus.Count;

        _ = App.LogError($"Recovery validation: {healthyCassettes}/{totalCassettes} cassettes healthy", LogType.Withdraw, null);

        // Check if 2K cassette (typically cassette 2) is now healthy
        var cassette2 = postRecoveryStatus.FirstOrDefault(c => c.No == 2);
        if (cassette2 != null)
        {
            if (cassette2.Status == "No Issues" || cassette2.Status == "Low Level")
            {
                _ = App.LogError("2K Cassette (C2) recovery successful", LogType.Withdraw, null);
            }
            else
            {
                _ = App.LogError($"2K Cassette (C2) still has issues: {cassette2.Status}", LogType.Withdraw, null);

                // Log recommendation for manual intervention
                _ = App.LogError("RECOMMENDATION: 2K cassette may require manual reseating or maintenance", LogType.Error, null);
            }
        }

        // Track recovery metrics
        _ = App.TrackAtmRealTime(new UpdateAtmRealTimeRequestModel()
        {
            WithdrawMoveForwardStatus = $"Recovery completed - {healthyCassettes}/{totalCassettes} healthy",
        });

        return Task.CompletedTask;
    }
}
