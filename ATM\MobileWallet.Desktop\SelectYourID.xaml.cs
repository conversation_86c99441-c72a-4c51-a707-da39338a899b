﻿using System.ComponentModel;
using System.Windows;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Client;

namespace MobileWallet.Desktop
{
    /// <summary>
    /// Interaction logic for Window2.xaml
    /// </summary>
    public partial class SelectYourID : Window
    {
        public SelectYourID()
        {
            InitializeComponent();
            Set_Language();
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            SelectLanguage NewWindow = new SelectLanguage();
            NewWindow.Show();
            this.Close();
        }

        private void Button_Click_1(object sender, RoutedEventArgs e)
        {
            _ = App.LogError("ID Card Button Pressed on Select ID", LogType.IdCard);
            CaptureIdCard NewWindow = new CaptureIdCard();
            NewWindow.Show();
            this.Close();
        }

        private void Button_Click_2(object sender, RoutedEventArgs e)
        {
            _ = App.LogError("Cancel Button Pressed on Select ID", LogType.Cancel);
            WelcomeToMobileWallet mainWindow = new WelcomeToMobileWallet();
            mainWindow.Show();
            this.Close();
        }

        private void Button_Click_Passport(object sender, RoutedEventArgs e)
        {
            try
            {
                App.ShowProcessingDialogWithMessage(
                    ResourceEnglish.PleaseWait,
                    ResourceFrench.PleaseWait
                );
                _ = App.LogError("Passport Button Pressed on Select ID", LogType.Passport);
                App.HideProcessingDialog();
            }
            catch (Exception exception)
            {
                App.AppLogger.Error(exception, exception.Message);
                App.HideProcessingDialog();
            }

            if (Global.UseV2)
            {
                CapturePassportV2 NewWindow = new CapturePassportV2();
                NewWindow.Show();
            }
            else
            {
                CapturePassport NewWindow = new CapturePassport();
                NewWindow.Show();
            }
            this.Close();
        }

        private void Set_Language()
        {
            switch (Global.DefaultLanguage)
            {
                case "English":
                    IDCard.Content = ResourceEnglish.IDCard;
                    Passport.Content = ResourceEnglish.Passport;
                    Cancel.Content = ResourceEnglish.Cancel;
                    Back.Content = ResourceEnglish.Back;
                    SelectIDScanner.Text = ResourceEnglish.SelectIdentificationMethod;
                    break;
                case "French":
                    IDCard.Content = ResourceFrench.IDCard;
                    Back.Content = ResourceFrench.Back;
                    Cancel.Content = ResourceFrench.Cancel;
                    Passport.Content = ResourceFrench.Passport;
                    SelectIDScanner.Text = ResourceFrench.SelectIdentificationMethod;
                    break;
            }
        }

        private void Button_Click_Back(object sender, RoutedEventArgs e)
        {
            _ = App.LogError("Back Button Pressed on Select ID", LogType.Back);
            SelectMobileMoney mainWindow = new SelectMobileMoney();
            mainWindow.Show();
            this.Close();
        }

        private void SelectYourID_OnLoaded(object sender, RoutedEventArgs e)
        {
            Helper.AdjustRowHeight(this, DynamicRow);
            App.StartTimer(this);
            _ = App.TrackAtmRealTime(
                new UpdateAtmRealTimeRequestModel() { CurrentScreen = nameof(SelectYourID) }
            );
        }

        private void SelectYourID_OnClosing(object? sender, CancelEventArgs e)
        {
            App.StopTimer();
        }
    }
}
