﻿using System.ComponentModel;
using System.Net.Http;
using System.Windows;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Atm;
using MobileWallet.Desktop.Client;
using Newtonsoft.Json;

namespace MobileWallet.Desktop
{
    /// <summary>
    /// Interaction logic for Window3.xaml
    /// </summary>
    public partial class PlaceYourCardAlignWithTheCamera : Window
    {
        public PlaceYourCardAlignWithTheCamera()
        {
            InitializeComponent();
            Set_Language();
            //Open Camera
            // camera.OpenTheCamera();
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            _ = App.LogError("Back Button Pressed on Camera Page", LogType.Back);
            SelectYourID NewWindow = new SelectYourID();
            NewWindow.Show();
            this.Close();
        }

        private void Button_Click_Cancel(object sender, RoutedEventArgs e)
        {
            _ = App.LogError("Cancel Button Pressed on Camera Page", LogType.Cancel);
            WelcomeToMobileWallet NewWindow = new WelcomeToMobileWallet();
            NewWindow.Show();
            this.Close();
        }

        private void Set_Language()
        {
            switch (Global.DefaultLanguage)
            {
                case "English":
                    Capture.Content = ResourceEnglish.Capture;
                    Back.Content = ResourceEnglish.Back;
                    Cancel.Content = ResourceEnglish.Cancel;
                    PlaceCard.Text = ResourceEnglish.PlaceCard;
                    break;
                case "French":
                    Capture.Content = ResourceFrench.Capture;
                    Back.Content = ResourceFrench.Back;
                    Cancel.Content = ResourceFrench.Cancel;
                    PlaceCard.Text = ResourceFrench.Place_Your_Card_Align_With_The_Camera;
                    break;
            }
        }

        private void Button_Click_CaptureImage(object sender, RoutedEventArgs e)
        {
            try
            {
                App.ShowProcessingDialogWithMessage(
                    ResourceEnglish.ProcessingRequest,
                    ResourceFrench.ProcessingRequest
                );
                //Camera Implementation
                if (Global.UseHardware)
                {
                    if (!App.Passport.IsInitialized())
                    {
                        if (!App.Passport.StartEngine())
                        {
                            Console.WriteLine("Failed to start the reader engine.");
                            CustomMessageBox.Show(
                                "Passport/Id Card Scanner is not working at the moment."
                            );
                        }

                        if (!App.Passport.Initialize())
                        {
                            Console.WriteLine("Failed to start the reader engine.");
                            CustomMessageBox.Show(
                                "Passport/Id Card Scanner is not working at the moment."
                            );
                        }
                    }
                    var r = App.Passport.ManualRead();
                    if (r)
                    {
                        App.ShowProcessingDialogWithMessage(
                            ResourceEnglish.ProcessingRequest,
                            ResourceFrench.ProcessingRequest
                        );
                    }
                    else
                    {
                        CustomMessageBox.Show("Unable to Read Passport/ID Card");
                    }
                    return;
                }
                App.HideProcessingDialog();
            }
            catch (Exception ex)
            {
                App.AppLogger.Error(ex,ex.Message);
                App.HideProcessingDialog();
                CustomMessageBox.Show("An error occurred: " + ex.Message, "Error");
            }
        }

        private void PlaceYourCardAlignWithTheCamera_OnClosing(object? sender, CancelEventArgs e)
        {
            App.StopTimer();
            if (Global.UseHardware)
            {
                if (App.Passport.IsInitialized())
                {
                    App.Passport.DeInitialize();
                    App.Passport.StopEngine();
                }
            }
        }

        private void PlaceYourCardAlignWithTheCamera_OnLoaded(object sender, RoutedEventArgs e)
        {
            App.StartTimer(this);
            _ = App.TrackAtmRealTime(
                new UpdateAtmRealTimeRequestModel()
                {
                    CurrentScreen = nameof(PlaceYourCardAlignWithTheCamera),
                }
            );
        }
    }
}
