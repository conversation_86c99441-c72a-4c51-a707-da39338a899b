﻿using System.ComponentModel;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Documents;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Atm;
using MobileWallet.Desktop.Client;
using MobileWallet.Desktop.Helpers;

namespace MobileWallet.Desktop
{
    /// <summary>
    /// Interaction logic for PleaseInsertNote.xaml
    /// </summary>
    public partial class PleaseInsertNote : Window
    {
        public static string AccountNumber { get; set; }
        public static string TransactionID { get; set; }
        private List<ReceiptItem> items;
        private int previousTotalAmount = 0;

        public PleaseInsertNote()
        {
            InitializeComponent();
            Set_Language();
            BorderHelp.Visibility = Visibility.Collapsed;
        }

        private void Set_Language()
        {
            switch (Global.DefaultLanguage)
            {
                case "English":
                    TxtInsertNote.Inlines.Clear();
                    TxtInsertNote.Inlines.Add($"{Global.Username} {Global.CurrentAccountNumber}");
                    TxtInsertNote.Inlines.Add(new LineBreak());
                    TxtInsertNote.Inlines.Add($"Insert your bills, then validate.");
                    TxtInsertNote.Inlines.Add(new LineBreak());
                    if (Global.IsCrypto)
                    {
                        TxtInsertNote.Inlines.Add($"(from 5000 Fcfa to 300,000 Fcfa)");
                    }
                    else
                    {
                        TxtInsertNote.Inlines.Add($"(from 5000 Fcfa to 300,000 Fcfa)");
                    }
                    TxtEscrow.Text = "Deposit Amount: 0";
                    Back.Content = ResourceEnglish.Back;
                    Cancel.Content = ResourceEnglish.Cancel;
                    Submit.Content = ResourceEnglish.Submit;
                    break;
                case "French":
                    TxtInsertNote.Inlines.Clear();
                    TxtInsertNote.Inlines.Add($"{Global.Username} {Global.CurrentAccountNumber}");
                    TxtInsertNote.Inlines.Add(new LineBreak());
                    TxtInsertNote.Inlines.Add($"Insérez vos billets, puis validez.");
                    TxtInsertNote.Inlines.Add(new LineBreak());
                    if (Global.IsCrypto)
                    {
                        TxtInsertNote.Inlines.Add($"(de 5000 Fcfa à 300,000 Fcfa)");
                    }
                    else
                    {
                        TxtInsertNote.Inlines.Add($"(de 5000 Fcfa à 300,000 Fcfa)");
                    }
                    TxtEscrow.Text = "Montant du dépôt : 0";
                    Back.Content = ResourceFrench.Back;
                    Cancel.Content = ResourceFrench.Cancel;
                    Submit.Content = ResourceFrench.Submit;
                    break;
            }
        }

        public PleaseInsertNote(int previousTotalAmount, List<ReceiptItem> items)
        {
            InitializeComponent();
            this.previousTotalAmount = previousTotalAmount;
            this.items = items;
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            ReturnEscrow();
            EnterAccountNumber NewWindow = new EnterAccountNumber();
            NewWindow.Show();
            Close();
        }

        private async Task HandleCrypto(int cashBoxTotal, string accountNumber)
        {
            var bitCoinClient = new CryptoClient(HttpClientSingleton.Instance);
            var isBuy = Global.IsDeposit;
            var data = await new CryptoClient(
                HttpClientSingleton.Instance
            ).Crypto_GetCryptoQuoteAsync(
                isBuy,
                cashBoxTotal,
                Global.SelectedToken.Address,
                Global.Currency
            );
            var totalAmountToSend = data.Quote;
            if (totalAmountToSend < 0)
            {
                App.HideProcessingDialog();
                CustomMessageBox.Show("Cannot process Low Amount");
                return;
            }
            var dialogResult = new SelectCryptoRateInfoDialog(
                data,
                cashBoxTotal,
                false,
                "Confirm Transaction"
            ).ShowDialog();
            // Global.IsFrench
            //     ? $"Montant inséré actuel : {cashBoxTotal}\n"
            //       + $"{cashBoxTotal} XAF = {data.Quote} {Global.SelectedToken.Symbol}\n"
            //       + $"Frais de gaz : {data.GasFee}\n"
            //       + $"Vous allez recevoir : {totalAmountToSend} {Global.SelectedToken.Symbol}\n"
            //       + "Voulez-vous continuer ?"
            //     : $"Current Inserted Amount: {cashBoxTotal}\n"
            //       + $"{cashBoxTotal} XAF = {data.Quote} {Global.SelectedToken.Symbol}\n"
            //       + $"Gas Fee: {data.GasFee}\n"
            //       + $"You will receive:{totalAmountToSend} {Global.SelectedToken.Symbol}\n"
            //       + $"Do you want to proceed?",
            // "",
            // MessageBoxButton.YesNo
            if (dialogResult == true)
            {
                var result = await bitCoinClient.Crypto_CreateCryptoQuoteAsync(
                    new CreateCryptoQuoteRequestModel()
                    {
                        SessionId = TokenManager.SessionId,
                        Address = Global.SelectedToken.Address,
                        AmountIn = cashBoxTotal,
                        AmountOut = data.Quote,
                        IsBuy = true,
                        UserAddress = Global.UserAddress,
                        PhoneNumber = Global.CurrentAccountNumber,
                        AvgPrice = data.AvgPrice,
                        TotalLocal = data.TotalLocal,
                        TotalUsd = data.TotalUsd,
                        Currency = Global.Currency,
                    }
                );
                if (result != null)
                {
                    while (true)
                    {
                        await Task.Delay(30000);
                        var status = "PENDING";
                        var reason = "";
                        try
                        {
                            var response = (
                                await bitCoinClient.Crypto_GetCryptoQuoteByIdAsync(
                                    result.Data.TransactionId,
                                    TokenManager.SessionId
                                )
                            ).Data;
                            status = response.Status;
                            result.Data.TxHash = response.TransactionHash;
                        }
                        catch (Exception exception)
                        {
                            App.AppLogger.Error(exception, exception.Message);
                            _ = App.LogError(
                                "ERROR in INSERT NOTE",
                                LogType.Error,
                                exception.ToString()
                            );
                            Console.WriteLine(exception);
                        }

                        if (status.ToUpper() == "PENDING")
                        {
                            continue;
                        }

                        if (status.ToUpper() != "SUCCESS")
                        {
                            App.HideProcessingDialog();
                            CustomMessageBox.Show("Transaction Failed: " + status);
                            ReturnEscrow();
                            WelcomeToMobileWallet window = new WelcomeToMobileWallet();
                            window.Show();
                            Close();
                            return;
                        }

                        break;
                    }

                    _ = App.TrackAtmRealTime(
                        new UpdateAtmRealTimeRequestModel() { IsDeposit = true }
                    );
                    var notes = GetTotalEscrowWithNotes().ToList();
                    if (Global.UseHardware)
                    {
                        string stackResult = App.CashAcceptor.ecrow_stack();
                        string closeResult = App.CashAcceptor.close_acceptor();
                    }

                    _ = App.LogError(
                        "Cash Acceptor Accepted: " + cashBoxTotal,
                        LogType.NoteAccepted,
                        ""
                    );
                    string branchCode = TokenManager.UserName;
                    string name = Global.Username;
                    double cashOut = cashBoxTotal;
                    string transactionId = result.Data.TransactionId;
                    string date = DateTime.Now.ToString("yyyy-MM-dd");
                    string time = DateTime.Now.ToString("hh:mm tt");
                    string currency = "XAF";
                    string accountNo = accountNumber;
                    var item = notes
                        .OrderByDescending(p => p.Key)
                        .ToList()
                        .Select(p => new ReceiptItem()
                        {
                            Item = p.Key,
                            Quantity = p.Value,
                            Amount = p.Key * p.Value,
                        })
                        .ToList();
                    var isValid = ReceiptPrinter.IsValid();
                    if (isValid == 0)
                    {
                        bool isSuccess = ReceiptPrinter.PrintDepositReceiptCrypto(
                            branchCode,
                            name,
                            cashOut,
                            transactionId,
                            date,
                            time,
                            currency,
                            accountNo,
                            totalAmountToSend.ToString("N1"),
                            Global.SelectedToken.Symbol,
                            data.GasFee.ToString("N"),
                            data.Quote.ToString("N6"),
                            Global.SelectedToken.Address,
                            Global.UserAddress,
                            result.Data.TxHash,
                            item,
                            $"1 {Global.SelectedNetwork.Symbol} = $" + data.AvgPrice.ToString("F4"),
                            "1 USD = " + data.ExchangeRate + " " + Global.Currency,
                            "$" + data.TotalUsd,
                            "CONFIRMED"
                        );
                        _ = App.LogError("Deposit Print Completed", LogType.Deposit, "");
                    }
                    else
                    {
                        _ = App.LogError("Printer Error", LogType.Error, "Error Code: " + isValid);
                        CustomMessageBox.Show("Receipt is not available at the moment");
                    }

                    App.HideProcessingDialog();
                    ReturnEscrow();
                    ThankYou processingScreen = new ThankYou();
                    processingScreen.Show();
                    Close();
                    return;
                }
            }

            if (Global.UseHardware)
            {
                var stackResult = App.CashAcceptor.return_escrow_stack();
                var events = App.CashAcceptor.events.ToList();
                // Close the bill acceptor:
                var closeResult = App.CashAcceptor.close_acceptor();
            }

            _ = App.LogError("Cash Acceptor Cancelled: " + cashBoxTotal, LogType.Cancel, "");
            App.HideProcessingDialog();
            CustomMessageBox.ShowDialog(
                Global.IsFrench
                    ? "Vous avez annulé la transaction. Veuillez réessayer."
                    : "You have cancelled transaction. Please try again."
            );
            ReturnEscrow();
            WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
            welcomeToMobileWallet.Show();
            Close();
        }

        private async Task HandleMtn(int cashBoxTotal, string accountNumber)
        {
            var transactionClient = new MtnClient(HttpClientSingleton.Instance);
            var balance = await new MtnClient(HttpClientSingleton.Instance).Mtn_GetBalanceAsync();
            if (balance == null)
            {
                CustomMessageBox.Show("The system has no funds available");
                return;
            }

            if (balance.Data < cashBoxTotal)
            {
                CustomMessageBox.Show("The system has insufficient funds");
                return;
            }

            _ = App.LogError("Notes Inserted:" + cashBoxTotal, LogType.NoteInserted, null);
            if (Global.UseHardware)
            {
                var list = App.CashAcceptor.events.ToList();
                var state = App.CashAcceptor.State;
            }
            var dialogResult = true;
            // bool? dialogResult = CustomMessageBox.ShowDialog(
            //     Global.IsFrench
            //         ? $"Montant actuellement inséré : {cashBoxTotal}\nVoulez-vous continuer"
            //         : $"Current Inserted Amount: {cashBoxTotal}\nDo you want to proceed?",
            //     "",
            //     MessageBoxButton.OK
            // );
            if (dialogResult == false)
            {
                //Stack the escrowed document
                if (Global.UseHardware)
                {
                    string stackResult = App.CashAcceptor.return_escrow_stack();
                    var events = App.CashAcceptor.events.ToList();
                    string closeResult = App.CashAcceptor.close_acceptor();
                }

                _ = App.LogError("Cash Acceptor Cancelled: " + cashBoxTotal, LogType.Cancel, "");
                App.HideProcessingDialog();
                CustomMessageBox.ShowDialog(
                    Global.IsFrench
                        ? "Vous avez annulé la transaction. Veuillez réessayer."
                        : "You have cancelled transaction. Please try again."
                );
                ReturnEscrow();
                WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
                welcomeToMobileWallet.Show();
                Close();
            }
            else
            {
                var result = await transactionClient.Mtn_CreateTransferRequestAsync(
                    new CreateTransferRequestRequestModel()
                    {
                        Amount = cashBoxTotal,
                        PhoneNumber = accountNumber,
                        SessionId = TokenManager.SessionId,
                        Currency = Global.Currency,
                    }
                );
                while (true)
                {
                    await Task.Delay(5000);
                    var status = "PENDING";
                    var reason = "";
                    try
                    {
                        var response = (
                            await transactionClient.Mtn_GetTransactionInfoAsync(
                                result.Data.TransactionId
                            )
                        ).Data;
                        status = response?.Status;
                        reason = response?.Reason;
                    }
                    catch (Exception exception)
                    {
                        App.AppLogger.Error(exception, exception.Message);
                        _ = App.LogError(
                            "ERROR in INSERT NOTE",
                            LogType.Error,
                            exception.ToString()
                        );
                        Console.WriteLine(exception);
                    }

                    if (status == "PENDING")
                    {
                        continue;
                    }

                    if (status != "SUCCESSFUL")
                    {
                        App.HideProcessingDialog();
                        CustomMessageBox.Show("Transaction Failed: " + reason);
                        ReturnEscrow();
                        WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
                        welcomeToMobileWallet.Show();
                        Close();
                        return;
                    }

                    break;
                }

                if (true)
                {
                    _ = App.TrackAtmRealTime(
                        new UpdateAtmRealTimeRequestModel() { IsDeposit = true }
                    );
                    var notes = GetTotalEscrowWithNotes().ToList();
                    if (Global.UseHardware)
                    {
                        string stackResult = App.CashAcceptor.ecrow_stack();
                        string closeResult = App.CashAcceptor.close_acceptor();
                    }

                    _ = App.LogError(
                        "Cash Acceptor Accepted: " + cashBoxTotal,
                        LogType.NoteAccepted,
                        ""
                    );
                    string branchCode = TokenManager.UserName;
                    string name = Global.Username;
                    double cashOut = cashBoxTotal;
                    string transactionId = result.Data.TransactionId;
                    string date = DateTime.Now.ToString("yyyy-MM-dd");
                    string time = DateTime.Now.ToString("hh:mm tt");
                    string currency = "XAF";
                    string accountNo = accountNumber;
                    // ReceiptPrinter.PrintDepositReceipt();
                    var item = notes
                        .OrderByDescending(p => p.Key)
                        .ToList()
                        .Select(p => new ReceiptItem()
                        {
                            Item = p.Key,
                            Quantity = p.Value,
                            Amount = p.Key * p.Value,
                        })
                        .ToList();

                    App.HideProcessingDialog();
                    var isValid = ReceiptPrinter.IsValid();
                    if (isValid == 0)
                    {
                        bool isSuccess = ReceiptPrinter.PrintDepositReceipt(
                            branchCode,
                            name,
                            cashOut,
                            transactionId,
                            date,
                            time,
                            currency,
                            accountNo,
                            item
                        );
                        _ = App.LogError("Deposit Print Completed", LogType.Deposit, "");
                    }
                    else
                    {
                        _ = App.LogError("Printer Error", LogType.Error, "Error Code: " + isValid);
                        CustomMessageBox.Show("Receipt is not available at the moment");
                    }

                    // Go to ProcessingScreen
                    ReturnEscrow();
                    ThankYou processingScreen = new ThankYou();
                    processingScreen.Show();
                    Close();
                }
            }
        }

        public async void Button_DepositSubmit(object sender, RoutedEventArgs e)
        {
            try
            {
                ButtonHelper.ToggleButton(sender);
                App.ShowProcessingDialogWithMessage(
                    ResourceEnglish.TransactionProcessing,
                    ResourceFrench.TransactionProcessing
                );
                string accountNumber = Global.CurrentAccountNumber;
                var cashBoxTotal = 0;
                if (!Global.UseHardware)
                {
                    cashBoxTotal = 500;
                    if (!Global.IsCrypto)
                    {
                        await HandleMtn(cashBoxTotal, accountNumber);
                    }
                    else
                    {
                        cashBoxTotal = 5000;
                        await HandleCrypto(cashBoxTotal, accountNumber);
                    }
                }
                else if (App.CashAcceptor.events.Contains("Event: Connected."))
                {
                    cashBoxTotal = GetTotalEscrow();
                    if (cashBoxTotal < 500)
                    {
                        App.HideProcessingDialog();
                        CustomMessageBox.Show("Amount is less than 500");
                        return;
                    }
                    if (Global.IsCrypto)
                    {
                        await HandleCrypto(cashBoxTotal, accountNumber);
                    }
                    else
                    {
                        await HandleMtn(cashBoxTotal, accountNumber);
                    }
                }
                else if (App.CashAcceptor.events.Contains("Event: Jam Detected"))
                {
                    //Stack the escrowed document
                    string stackResult = App.CashAcceptor.return_escrow_stack();
                    // Close the bill acceptor:
                    string closeResult = App.CashAcceptor.close_acceptor();
                    _ = App.LogError(
                        "Acceptor Error",
                        LogType.Error,
                        $"Event: Jam Detected,Stack:{stackResult},Close:{closeResult}"
                    );
                    CustomMessageBox.Show(
                        "Jam Detected! Please remove the jammed bills and try again."
                    );
                    ReturnEscrow();
                    WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
                    welcomeToMobileWallet.Show();
                    Close();
                }
                else
                {
                    string stackResult = App.CashAcceptor.return_escrow_stack();
                    string closeResult = App.CashAcceptor.close_acceptor();
                    _ = App.LogError(
                        "Acceptor Error",
                        LogType.Error,
                        $"Unable to establish a connection,Stack:{stackResult},Close:{closeResult}"
                    );
                    CustomMessageBox.Show(
                        "Unable to establish a connection with the bill acceptor. Please try again later."
                    );
                    ReturnEscrow();
                    WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
                    welcomeToMobileWallet.Show();
                    Close();
                }
            }
            catch (ApiException apiException)
            {
                App.HideProcessingDialog();
                CustomMessageBox.Show("Unable to process Transaction");
            }
            catch (Exception exception)
            {
                App.AppLogger.Error(exception, exception.Message);
                App.HideProcessingDialog();
                CustomMessageBox.Show("Unable to process Transaction");
                Console.WriteLine(exception);
            }
            finally
            {
                ButtonHelper.ToggleButton(sender);
            }
        }

        private void Button_Click_2(object sender, RoutedEventArgs e)
        {
            ReturnEscrow();
            WelcomeToMobileWallet NewWindow = new WelcomeToMobileWallet();
            NewWindow.Show();
            Close();
        }

        private void OnWindowLoad(object sender, RoutedEventArgs e)
        {
            Helper.AdjustRowHeight(this, DynamicRow);
            _ = App.TrackAtmRealTime(
                new UpdateAtmRealTimeRequestModel() { CurrentScreen = nameof(PleaseInsertNote) }
            );
            if (Global.UseHardware)
            {
                App.CashAcceptor.events.Clear();
                App.CashAcceptor.close_acceptor();
                App.CashAcceptor.open_acceptor();
                App.CashAcceptor.OnEscrow += Escrow;
                App.CashAcceptor.OnStack += OnStack;
            }
        }

        private void OnStack(object? sender, string e)
        {
            _ = App.LogError("Cash Acceptor Note Stacked: " + e, LogType.NoteInserted, "");
            TxtEscrow.Dispatcher.Invoke(() =>
            {
                BorderHelp.Visibility = Visibility.Visible;
                switch (Global.DefaultLanguage)
                {
                    case "English":
                        TxtEscrow.Text =
                            "Deposit Amount: "
                            + GetTotalEscrow().ToString(CultureInfo.InvariantCulture);
                        break;
                    case "French":
                        TxtEscrow.Text =
                            "Montant du dépôt : "
                            + GetTotalEscrow().ToString(CultureInfo.InvariantCulture);
                        break;
                }
            });
        }

        private void Escrow(object? sender, string e)
        {
            _ = App.TrackAtmRealTime(
                new UpdateAtmRealTimeRequestModel() { DepositNotes = GetTotalEscrow().ToString() }
            );
            _ = App.LogError("Cash Acceptor Note Inserted: " + e, LogType.NoteInserted, "");
            App.CashAcceptor.ecrow_stack();
        }

        private Dictionary<int, int> GetTotalEscrowWithNotes()
        {
            var totalEscrow = new Dictionary<int, int>();
            if (!Global.UseHardware)
            {
                totalEscrow.Add(1000, 10);
                return totalEscrow;
            }

            foreach (var item1 in App.CashAcceptor.events.ToList())
            {
                // Check if the event is "Escrowed"
                if (item1.StartsWith("Event: Escrowed"))
                {
                    // Extract the numeric value
                    Match match = Regex.Match(item1, @"XAF (\d+)");

                    if (match.Success)
                    {
                        // Parse the extracted value and add it to the total
                        int value = int.Parse(match.Groups[1].Value);
                        if (!totalEscrow.TryAdd(value, 1))
                        {
                            totalEscrow[value]++;
                        }
                    }
                }
            }

            return totalEscrow;
        }

        private int GetTotalEscrow()
        {
            var CashBoxTotal = 0;
            foreach (var item1 in App.CashAcceptor.events.ToList())
            {
                // Check if the event is "Escrowed"
                if (item1.StartsWith("Event: StackedWithDocInfo"))
                {
                    // Extract the numeric value
                    Match match = Regex.Match(item1, @"XAF (\d+)");

                    if (match.Success)
                    {
                        // Parse the extracted value and add it to the total
                        int value = int.Parse(match.Groups[1].Value);
                        CashBoxTotal += value;
                    }
                }
            }

            return CashBoxTotal;
        }

        private void ReturnEscrow()
        {
            if (Global.UseHardware)
            {
                App.CashAcceptor.return_escrow_stack();
                if (App.CashAcceptor.OnEscrow != null)
                {
                    App.CashAcceptor.OnEscrow -= Escrow;
                }
                if (App.CashAcceptor.OnStack != null)
                {
                    App.CashAcceptor.OnStack -= OnStack;
                }
                App.CashAcceptor.close_acceptor();
            }
        }

        private void Button_AddMore(object sender, RoutedEventArgs e)
        {
            CustomMessageBox.Show("Please Contact the assistance in the bank, for amount reversal");
        }

        private void PleaseInsertNote_OnClosing(object? sender, CancelEventArgs e)
        {
            App.DisposeOnlyCashAcceptor();
        }
    }
}
