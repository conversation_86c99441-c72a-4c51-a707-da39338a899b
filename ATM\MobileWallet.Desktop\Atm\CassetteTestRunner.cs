using System;
using System.Threading.Tasks;

namespace MobileWallet.Desktop.Atm
{
    /// <summary>
    /// Test runner for cassette validation and diagnostics
    /// </summary>
    public static class CassetteTestRunner
    {
        /// <summary>
        /// Run comprehensive 2K cassette diagnostics
        /// </summary>
        public static async Task<bool> RunComprehensiveDiagnostics()
        {
            try
            {
                _ = App.LogError("=== STARTING COMPREHENSIVE 2K CASSETTE DIAGNOSTICS ===", LogType.Withdraw, null);
                
                var allPassed = true;
                
                // Phase 1: Software validation tests
                _ = App.LogError("PHASE 1: Software Validation", LogType.Withdraw, null);
                allPassed &= CassetteValidationTests.RunAll2KCassetteTests();
                
                // Phase 2: Hardware status check
                _ = App.LogError("PHASE 2: Hardware Status Check", LogType.Withdraw, null);
                allPassed &= await RunHardwareStatusCheck();
                
                // Phase 3: Communication test
                _ = App.LogError("PHASE 3: Communication Test", LogType.Withdraw, null);
                allPassed &= await RunCommunicationTest();
                
                // Phase 4: Real transaction simulation
                _ = App.LogError("PHASE 4: Transaction Simulation", LogType.Withdraw, null);
                allPassed &= await RunTransactionSimulation();
                
                var result = allPassed ? "PASSED" : "FAILED";
                _ = App.LogError($"=== COMPREHENSIVE DIAGNOSTICS {result} ===", allPassed ? LogType.Withdraw : LogType.Error, null);
                
                return allPassed;
            }
            catch (Exception ex)
            {
                _ = App.LogError($"Error running comprehensive diagnostics: {ex.Message}", LogType.Error, ex.StackTrace);
                return false;
            }
        }

        /// <summary>
        /// Test hardware status reading and validation
        /// </summary>
        private static async Task<bool> RunHardwareStatusCheck()
        {
            try
            {
                if (!Global.UseHardware)
                {
                    _ = App.LogError("Hardware disabled - skipping hardware tests", LogType.Withdraw, null);
                    return true;
                }

                // Read cassette status multiple times to check for consistency
                var allPassed = true;
                
                for (int attempt = 1; attempt <= 3; attempt++)
                {
                    _ = App.LogError($"Hardware status check attempt {attempt}/3", LogType.Withdraw, null);
                    
                    var status = App.Dispenser.ReadCassetteStatus();
                    CassetteLogger.LogCassetteStatus(status, $"Hardware Check {attempt}");
                    
                    if (!status.Any())
                    {
                        _ = App.LogError($"No cassette status returned on attempt {attempt}", LogType.Error, null);
                        allPassed = false;
                        continue;
                    }
                    
                    // Check 2K cassette specifically
                    var cassette2 = status.FirstOrDefault(c => c.No == 2);
                    if (cassette2 == null)
                    {
                        _ = App.LogError($"2K Cassette not found in status on attempt {attempt}", LogType.Error, null);
                        allPassed = false;
                    }
                    else
                    {
                        _ = App.LogError($"2K Cassette status on attempt {attempt}: {cassette2.Status}", LogType.Withdraw, null);
                        
                        if (cassette2.Status != "No Issues" && cassette2.Status != "Low Level")
                        {
                            _ = App.LogError($"2K Cassette has issue: {cassette2.Status}", LogType.Error, null);
                            
                            // Attempt recovery
                            _ = App.LogError("Attempting 2K cassette recovery...", LogType.Withdraw, null);
                            await WithdrawHelper.HandleErrorWithCassetteRecovery($"2K Cassette Issue: {cassette2.Status}");
                        }
                    }
                    
                    await Task.Delay(2000); // Wait between attempts
                }
                
                return allPassed;
            }
            catch (Exception ex)
            {
                _ = App.LogError($"Error in hardware status check: {ex.Message}", LogType.Error, ex.StackTrace);
                return false;
            }
        }

        /// <summary>
        /// Test communication with dispenser
        /// </summary>
        private static async Task<bool> RunCommunicationTest()
        {
            try
            {
                if (!Global.UseHardware)
                {
                    _ = App.LogError("Hardware disabled - skipping communication tests", LogType.Withdraw, null);
                    return true;
                }

                var allPassed = true;
                
                // Test basic commands
                var commands = new[]
                {
                    new { Name = "Reset", Action = new Func<string?>(() => App.Dispenser.Reset()) },
                    new { Name = "OpenCassette", Action = new Func<string?>(() => App.Dispenser.OpenCassette()) },
                    new { Name = "ReadCassette", Action = new Func<string?>(() => App.Dispenser.ReadCassette()) }
                };
                
                foreach (var command in commands)
                {
                    try
                    {
                        _ = App.LogError($"Testing {command.Name} command...", LogType.Withdraw, null);
                        
                        var result = command.Action();
                        var success = result == null;
                        allPassed &= success;
                        
                        var status = success ? "PASS" : "FAIL";
                        _ = App.LogError($"  {command.Name}: {status} {(result != null ? $"- {result}" : "")}", 
                            success ? LogType.Withdraw : LogType.Error, null);
                        
                        await Task.Delay(1000); // Wait between commands
                    }
                    catch (Exception ex)
                    {
                        allPassed = false;
                        _ = App.LogError($"  {command.Name}: FAIL - Exception: {ex.Message}", LogType.Error, null);
                    }
                }
                
                return allPassed;
            }
            catch (Exception ex)
            {
                _ = App.LogError($"Error in communication test: {ex.Message}", LogType.Error, ex.StackTrace);
                return false;
            }
        }

        /// <summary>
        /// Simulate transactions that previously failed
        /// </summary>
        private static async Task<bool> RunTransactionSimulation()
        {
            try
            {
                // Test the problematic amounts that were failing
                var problematicAmounts = new[] { 12000, 14000 };
                var allPassed = true;
                
                foreach (var amount in problematicAmounts)
                {
                    _ = App.LogError($"Simulating transaction for amount: {amount}", LogType.Withdraw, null);
                    
                    try
                    {
                        // Get current cassette status
                        var cassettes = App.Dispenser.ReadCassetteStatus();
                        CassetteLogger.LogCassetteStatus(cassettes, $"Pre-simulation {amount}");
                        
                        // Check if currency is available
                        var isAvailable = await WithdrawHelper.IsAvailableCurrency(amount, cassettes);
                        
                        if (isAvailable)
                        {
                            _ = App.LogError($"  Amount {amount}: AVAILABLE ✅", LogType.Withdraw, null);
                            
                            // Log what the distribution would be
                            var client = new DenominationClient(HttpClientSingleton.Instance);
                            var response = await client.Denomination_GetDenominationByAtmAsync();
                            var list = response.Data?.ToList() ?? new List<DenominationRecordDto>();
                            list = list.Where(p => p.CassetteNo != "4").OrderByDescending(p => p.Denomination).ToList();
                            
                            var notes = WithdrawHelper.GetNotes(list, amount, cassettes);
                            CassetteLogger.LogDenominationCalculation(list, amount, notes);
                        }
                        else
                        {
                            _ = App.LogError($"  Amount {amount}: NOT AVAILABLE ❌", LogType.Error, null);
                            allPassed = false;
                        }
                    }
                    catch (Exception ex)
                    {
                        _ = App.LogError($"  Amount {amount}: ERROR - {ex.Message}", LogType.Error, null);
                        allPassed = false;
                    }
                    
                    await Task.Delay(1000);
                }
                
                return allPassed;
            }
            catch (Exception ex)
            {
                _ = App.LogError($"Error in transaction simulation: {ex.Message}", LogType.Error, ex.StackTrace);
                return false;
            }
        }

        /// <summary>
        /// Quick diagnostic for immediate troubleshooting
        /// </summary>
        public static async Task<string> RunQuickDiagnostic()
        {
            try
            {
                _ = App.LogError("=== QUICK 2K CASSETTE DIAGNOSTIC ===", LogType.Withdraw, null);
                
                var issues = new List<string>();
                
                // Check cassette status
                var cassettes = App.Dispenser.ReadCassetteStatus();
                var cassette2 = cassettes.FirstOrDefault(c => c.No == 2);
                
                if (cassette2 == null)
                {
                    issues.Add("2K Cassette not detected");
                }
                else if (cassette2.Status != "No Issues" && cassette2.Status != "Low Level")
                {
                    issues.Add($"2K Cassette has status: {cassette2.Status}");
                }
                
                // Test 12K availability
                var is12KAvailable = await WithdrawHelper.IsAvailableCurrency(12000, cassettes);
                if (!is12KAvailable)
                {
                    issues.Add("12K amount not available");
                }
                
                // Test 14K availability  
                var is14KAvailable = await WithdrawHelper.IsAvailableCurrency(14000, cassettes);
                if (!is14KAvailable)
                {
                    issues.Add("14K amount not available");
                }
                
                var result = issues.Any() ? 
                    $"ISSUES FOUND: {string.Join(", ", issues)}" : 
                    "ALL CHECKS PASSED";
                
                _ = App.LogError($"Quick diagnostic result: {result}", issues.Any() ? LogType.Error : LogType.Withdraw, null);
                
                return result;
            }
            catch (Exception ex)
            {
                var error = $"Diagnostic failed: {ex.Message}";
                _ = App.LogError(error, LogType.Error, ex.StackTrace);
                return error;
            }
        }
    }
}
